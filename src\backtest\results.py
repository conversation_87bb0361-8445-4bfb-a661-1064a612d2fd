"""."""

import logging
from dataclasses import dataclass
from typing import TypedDict

import pandas as pd

from src.performance_metrics import PerformanceMetrics
from src.temp import AllMetricsDict, BacktestConfig, Snapshot, Trade, TradeSide

logger = logging.getLogger(__name__)


class TradesDict(TypedDict):
    """."""

    entry_date: pd.Timestamp
    exit_date: pd.Timestamp | None
    entry_price: float
    exit_price: float | None
    quantity: int
    side: TradeSide
    pnl: float
    pnl_pct: float
    duration_days: int
    max_adverse_excursion: float
    max_favorable_excursion: float
    entry_reason: str
    confidence: float


@dataclass
class BackTestResults:
    """Backtesting results."""

    def __init__(
        self,
        equity_curve: list[Snapshot],
        final_portfolio_value: float,
        trades_list: list[Trade],
        config: BacktestConfig,
    ):
        self.config = config
        self.final_portfolio_value = final_portfolio_value
        self.equity_curve = self.set_equity_curve(equity_curve)
        self.trades = self.set_trades(trades_list)
        self.metrics = self.set_metrics()
        self.total_return = self.set_total_return(final_portfolio_value)
        self.total_trades = self.set_total_trades(trades_list)
        self.winning_trades = self.set_winning_trades(trades_list)
        self.losing_trades = self.set_losing_trades(trades_list)

    def set_equity_curve(self, equity_curve: list[Snapshot]) -> pd.DataFrame:
        """."""
        return pd.DataFrame(equity_curve).set_index("date")

    def set_total_return(self, final_value: float) -> float:
        """."""
        capital = self.config.initial_capital
        return (final_value - capital) / capital

    def set_total_trades(self, trades: list[Trade]) -> int:
        """."""
        return len(trades)

    def set_winning_trades(self, trades: list[Trade]) -> int:
        """."""
        return sum(1 for t in trades if t.pnl > 0)

    def set_losing_trades(self, trades: list[Trade]) -> int:
        """."""
        return sum(1 for t in trades if t.pnl < 0)

    def set_trades(self, trades_list: list[Trade]) -> pd.DataFrame:
        """."""
        trades_data: list[TradesDict] = [
            {
                "entry_date": trade.entry_date,
                "exit_date": trade.exit_date,
                "entry_price": trade.entry_price,
                "exit_price": trade.exit_price,
                "quantity": trade.quantity,
                "side": trade.side.value,
                "pnl": trade.pnl,
                "pnl_pct": trade.pnl_pct,
                "duration_days": trade.duration.days if trade.duration else 0,
                "max_adverse_excursion": trade.max_adverse_excursion,
                "max_favorable_excursion": trade.max_favorable_excursion,
                "entry_reason": trade.entry_signal.reason,
                "confidence": trade.entry_signal.confidence,
            }
            for trade in trades_list
        ]

        return pd.DataFrame(trades_data)

    def set_metrics(self) -> AllMetricsDict:
        """."""
        return PerformanceMetrics.calculate_metrics(
            self.equity_curve["portfolio_value"],
            self.trades,
            self.config.initial_capital,
        )
