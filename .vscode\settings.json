{
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.wordWrapColumn": 79,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      // "source.fixAll.pylance": "explicit",
      "source.fixAll.ruff": "explicit",
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit",
      "source.removeUnusedImports": "explicit"
    }
  },
  "python.defaultInterpreterPath": "./.venv/Scripts/python.exe",
}