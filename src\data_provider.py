"""Data provider module for stock market data ingestion from Polygon.io."""

import logging
import time
from typing import Any

import pandas as pd
from polygon.rest import RESTClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PolygonDataProvider:
    """Data provider for Polygon.io API integration."""

    def __init__(self, api_key: str):
        r"""Initialize the Polygon.io data provider.

        Args:
            api_key: Polygon.io API key.
                This should be set in \.env file as POLYGON_API_KEY.

        """
        self.rest_client = RESTClient(api_key)
        # TODO: Apparently, api key is read automatically. Investigate.
        # Cache is problematic, because obstruct tests.
        self.rate_limit_delay = 12  # 5 calls per minute for free tier

    def get_historical_data(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        timespan: str = "day",
        multiplier: int = 1,
    ) -> pd.DataFrame:
        """Fetch historical stock data from Polygon.io.

        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            timespan: Timespan (day, hour, minute)
            multiplier: Multiplier for timespan

        Returns:
            DataFrame with OHLCV data

        """
        try:
            logger.info(
                f"Fetching data for {symbol} from {start_date} to {end_date}",
            )
            aggs = self.rest_client.get_aggs(
                ticker=symbol,
                multiplier=multiplier,
                timespan=timespan,
                from_=start_date,
                to=end_date,
                adjusted=True,
                sort="asc",
                limit=50000,  # large enough for most use cases
            )
            if not aggs:
                logger.warning(f"No data found for {symbol}")
                return pd.DataFrame()
            df = pd.DataFrame([bar.__dict__ for bar in aggs])
            column_mapping = {
                "o": "open",
                "h": "high",
                "l": "low",
                "c": "close",
                "v": "volume",
                "t": "timestamp",
            }
            # Some versions use 't', some 'timestamp'
            for k, v in column_mapping.items():
                if k in df.columns:
                    df = df.rename(columns={k: v})
            if "timestamp" in df.columns:
                df["date"] = pd.to_datetime(df["timestamp"], unit="ms")
                df = df.set_index("date")
            # Select relevant columns
            cols = [
                col
                for col in ["open", "high", "low", "close", "volume"]
                if col in df.columns
            ]
            df = df[cols]
            df = df.sort_index()
            logger.info(f"Successfully fetched {len(df)} records for {symbol}")
            return df
        except Exception as e:
            logger.exception(f"Error fetching data for {symbol}: {e}")
            raise

    def get_ticker_details(self, symbol: str) -> dict[str, Any]:
        """Get ticker details from Polygon.io.

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary with ticker details

        """
        try:
            details = self.rest_client.get_ticker_details(symbol)
            if hasattr(details, "results"):
                return details.results
            return details if details else {}
        except Exception as e:
            logger.exception(
                f"Error fetching ticker details for {symbol}: {e}",
            )
            return {}

    # def search_tickers(self, query: str, market: str = "stocks") -> list:
    #     """Search for tickers matching the query.

    #     Args:
    #         query: Search query
    #         market: Market type (stocks, crypto, fx)

    #     Returns:
    #         List of matching tickers

    #     """
    #     try:
    #         tickers = self.rest_client.reference_tickers(
    #             search=query,
    #             market=market,
    #             active=True,
    #             limit=20,
    #         )
    #         return (
    #             tickers.results
    #             if hasattr(tickers, "results")
    #             else (tickers or [])
    #         )
    #     except Exception as e:
    #         logger.exception(f"Error searching tickers: {e}")
    #         return []


class DataCache:
    """Simple caching mechanism for data."""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 3600  # 1 hour

    def make_key(self, *args: str) -> str:
        """Create a cache key from arguments.

        Args:
            *args: Variable string arguments to join

        Returns:
            Cache key string

        """
        return "__".join(args)

    def get(self, *args: str) -> pd.DataFrame | None:
        """Get cached data."""
        key = self.make_key(*args)

        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.cache_timeout:
                return data
            del self.cache[key]
        return None

    def set(self, data: pd.DataFrame, args: tuple[str, str, str]) -> None:
        """Set cached data.

        Args:
            data (pd.DataFrame): Data to cache
            args (tuple[str]): Arguments to create cache key

        """
        key = self.make_key(*args)
        self.cache[key] = (data.copy(), time.time())

    def delete(self, *args: str) -> None:
        """Delete cached data."""
        key = self.make_key(*args)
        if key in self.cache:
            del self.cache[key]

    def clear(self) -> None:
        """Clear all cached data."""
        self.cache.clear()


# Global cache instance
data_cache = DataCache()
