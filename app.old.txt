"""Stock Market Backtesting Application.

A comprehensive Streamlit application for backtesting trading strategies.
"""

import logging
import os
from datetime import date, datetime, timedelta
from typing import cast

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from dotenv import load_dotenv
from plotly.subplots import make_subplots

from src.backtester import BacktestConfig, BacktestEngine, BackTestResults
from src.data_provider import PolygonDataProvider, data_cache
from src.strategies.base import (
    SignalType,
    StrategyConfig,
    TradingSignal,
)
from src.strategies.strategy_factory import create_strategy
from src.support_resistance import SupportResistanceFinder
from src.technical_indicators import TechnicalIndicators
from src.utils import DataUtils, ExportUtils, setup_logging

load_dotenv()

setup_logging()
logger = logging.getLogger(__name__)

st.set_page_config(
    page_title="Stock Market Backtester",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Initialize session state
if "data_provider" not in st.session_state:
    st.session_state.data_provider = None
if "current_data" not in st.session_state:
    st.session_state.current_data = None
if "backtest_results" not in st.session_state:
    st.session_state.backtest_results = None


def initialize_data_provider() -> PolygonDataProvider | None:
    """Initialize the data provider with API key."""
    try:
        api_key = os.getenv("POLYGON_API_KEY")
        if not api_key:
            st.error(
                "⚠️ Polygon.io API key not found."
                "Please set the POLYGON_API_KEY environment variable.",
            )
            st.info("You can get a free API key at https://polygon.io/")
            return None

        return PolygonDataProvider(api_key)

    except Exception as e:
        st.error(f"Error initializing data provider: {e}")
        return None


def load_data(
    symbol: str,
    start_date: date,
    end_date: date,
) -> pd.DataFrame | None:
    """Load historical data for the given symbol and date range."""
    try:
        if not st.session_state.data_provider:
            st.session_state.data_provider = initialize_data_provider()
            if not st.session_state.data_provider:
                return None

        # Check cache first
        cache_key = f"{symbol}_{start_date}_{end_date}"
        cached_data = data_cache.get(cache_key)
        if cached_data is not None:
            st.success("📁 Data loaded from cache")
            return cached_data

        # Show loading spinner
        with st.spinner(f"📊 Loading data for {symbol}..."):
            data = st.session_state.data_provider.get_historical_data(
                symbol=symbol,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d"),
            )

        if data.empty:
            st.error(f"No data found for {symbol} in the specified date range")
            return None

        # Validate and clean data
        if not DataUtils.validate_ohlcv_data(data):
            st.warning(
                "Data validation issues detected. Attempting to clean data...",
            )
            data = DataUtils.clean_data(data)

        # Cache the data
        data_cache.set(cache_key, data)

        st.success(f"✅ Successfully loaded {len(data)} records for {symbol}")
        return data

    except Exception as e:
        st.error(f"Error loading data: {e}")
        logger.exception(f"Error loading data for {symbol}: {e}")
        return None


def create_price_chart(
    data: pd.DataFrame,
    signals: list[TradingSignal] | None = None,
    sr_levels: dict | None = None,
) -> go.Figure:
    """Create an interactive price chart with indicators."""
    try:
        # Create subplots
        fig = make_subplots(
            rows=4,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.03,
            row_heights=[0.5, 0.2, 0.15, 0.15],
            subplot_titles=[
                "Price & Indicators",
                "Volume",
                "RSI",
                "Stochastic",
            ],
        )

        # Candlestick chart
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data["open"],
                high=data["high"],
                low=data["low"],
                close=data["close"],
                name="Price",
                increasing_line_color="#2ca02c",
                decreasing_line_color="#d62728",
            ),
            row=1,
            col=1,
        )

        # Add Bollinger Bands if available
        if "bb_upper" in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["bb_upper"],
                    name="BB Upper",
                    line={"color": "rgba(255,0,0,0.3)", "dash": "dash"},
                ),
                row=1,
                col=1,
            )
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["bb_middle"],
                    name="BB Middle",
                    line={"color": "rgba(0,0,255,0.5)"},
                ),
                row=1,
                col=1,
            )
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["bb_lower"],
                    name="BB Lower",
                    line={"color": "rgba(255,0,0,0.3)", "dash": "dash"},
                    fill="tonexty",
                    fillcolor="rgba(173,216,230,0.1)",
                ),
                row=1,
                col=1,
            )

        # Add moving averages if available
        if "ma_short" in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["ma_short"],
                    name="MA Short",
                    line={"color": "orange", "width": 1},
                ),
                row=1,
                col=1,
            )

        if "ma_long" in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["ma_long"],
                    name="MA Long",
                    line={"color": "purple", "width": 1},
                ),
                row=1,
                col=1,
            )

        # Add support/resistance levels
        if sr_levels:
            for level in sr_levels.get("support", []):
                fig.add_hline(
                    y=level,
                    line_dash="dash",
                    line_color="green",
                    annotation_text=f"Support: ${level:.2f}",
                    row=1,
                    col=1,
                )

            for level in sr_levels.get("resistance", []):
                fig.add_hline(
                    y=level,
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Resistance: ${level:.2f}",
                    row=1,
                    col=1,
                )

        # Add trading signals
        if signals:
            buy_signals = [
                s for s in signals if s.signal.value == SignalType.BUY.value
            ]
            sell_signals = [
                s for s in signals if s.signal.value == SignalType.SELL.value
            ]

            if buy_signals:
                fig.add_trace(
                    go.Scatter(
                        x=[s.timestamp for s in buy_signals],
                        y=[s.price for s in buy_signals],
                        mode="markers",
                        marker={
                            "symbol": "triangle-up",
                            "size": 12,
                            "color": "green",
                        },
                        name="Buy Signal",
                        text=[
                            f"Buy: ${s.price:.2f}<br>Confidence: {s.confidence:.2f}<br>{s.reason}"
                            for s in buy_signals
                        ],
                        hovertemplate="%{text}<extra></extra>",
                    ),
                    row=1,
                    col=1,
                )

            if sell_signals:
                fig.add_trace(
                    go.Scatter(
                        x=[s.timestamp for s in sell_signals],
                        y=[s.price for s in sell_signals],
                        mode="markers",
                        marker={
                            "symbol": "triangle-down",
                            "size": 12,
                            "color": "red",
                        },
                        name="Sell Signal",
                        text=[
                            f"Sell: ${s.price:.2f}<br>Confidence: {s.confidence:.2f}<br>{s.reason}"
                            for s in sell_signals
                        ],
                        hovertemplate="%{text}<extra></extra>",
                    ),
                    row=1,
                    col=1,
                )

        # Volume
        colors = [
            "red" if close_price < open_price else "green"
            for close_price, open_price in zip(
                data["close"],
                data["open"],
                strict=False,
            )
        ]
        fig.add_trace(
            go.Bar(
                x=data.index.to_numpy(),
                y=data["volume"],
                name="Volume",
                marker_color=colors,
                opacity=0.7,
            ),
            row=2,
            col=1,
        )

        # RSI
        if "rsi" in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["rsi"],
                    name="RSI",
                    line={"color": "purple"},
                ),
                row=3,
                col=1,
            )
            fig.add_hline(
                y=70,
                line_dash="dash",
                line_color="red",
                row=3,
                col=1,
            )
            fig.add_hline(
                y=30,
                line_dash="dash",
                line_color="green",
                row=3,
                col=1,
            )
            fig.add_hline(
                y=50,
                line_dash="dash",
                line_color="gray",
                row=3,
                col=1,
            )

        # Stochastic
        if "stoch_k" in data.columns and "stoch_d" in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["stoch_k"],
                    name="%K",
                    line={"color": "blue"},
                ),
                row=4,
                col=1,
            )
            fig.add_trace(
                go.Scatter(
                    x=data.index.to_numpy(),
                    y=data["stoch_d"],
                    name="%D",
                    line={"color": "red"},
                ),
                row=4,
                col=1,
            )
            fig.add_hline(
                y=80,
                line_dash="dash",
                line_color="red",
                row=4,
                col=1,
            )
            fig.add_hline(
                y=20,
                line_dash="dash",
                line_color="green",
                row=4,
                col=1,
            )

        # Update layout
        fig.update_layout(
            title="Price Chart with Technical Indicators",
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True,
            legend={"x": 0, "y": 1, "bgcolor": "rgba(255,255,255,0.8)"},
        )

        # Update y-axes
        fig.update_yaxes(title_text="Price ($)", row=1, col=1)
        fig.update_yaxes(title_text="Volume", row=2, col=1)
        fig.update_yaxes(title_text="RSI", row=3, col=1, range=[0, 100])
        fig.update_yaxes(title_text="Stochastic", row=4, col=1, range=[0, 100])

        return fig

    except Exception as e:
        st.error(f"Error creating price chart: {e}")
        return go.Figure()


def create_equity_curve_chart(results: BackTestResults) -> go.Figure:
    """Create equity curve chart."""
    try:
        equity_df = results["equity_curve"]

        fig = go.Figure()

        # Equity curve
        fig.add_trace(
            go.Scatter(
                x=equity_df.index.to_numpy(),
                y=equity_df["portfolio_value"],
                name="Portfolio Value",
                line={"color": "blue", "width": 2},
            ),
        )

        # Add buy and hold comparison if possible
        if "trades" in results and len(results["trades"]) > 0:
            trades_df = results["trades"]
            if len(trades_df) > 0:
                start_price = trades_df.iloc[0]["entry_price"]
                end_price = (
                    trades_df.iloc[-1]["exit_price"]
                    if trades_df.iloc[-1]["exit_price"]
                    else start_price
                )
                initial_capital = results["config"].initial_capital

                # Calculate buy and hold return
                buy_hold_return = end_price / start_price - 1
                buy_hold_final = initial_capital * (1 + buy_hold_return)

                # Create buy and hold line
                fig.add_trace(
                    go.Scatter(
                        x=[equity_df.index[0], equity_df.index[-1]],
                        y=[initial_capital, buy_hold_final],
                        name="Buy & Hold",
                        line={"color": "gray", "dash": "dash"},
                        opacity=0.7,
                    ),
                )

        fig.update_layout(
            title="Portfolio Equity Curve",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            height=400,
            showlegend=True,
        )

        return fig

    except Exception as e:
        st.error(f"Error creating equity curve: {e}")
        return go.Figure()


def create_drawdown_chart(results: BackTestResults) -> go.Figure:
    """Create drawdown chart."""
    try:
        equity_df = results["equity_curve"]

        # Calculate drawdown
        running_max = equity_df["portfolio_value"].expanding().max()
        drawdown = (
            (equity_df["portfolio_value"] - running_max) / running_max * 100
        )

        fig = go.Figure()

        fig.add_trace(
            go.Scatter(
                x=equity_df.index.to_numpy(),
                y=drawdown,
                name="Drawdown %",
                fill="tonexty",
                line={"color": "red"},
                fillcolor="rgba(255,0,0,0.3)",
            ),
        )

        fig.update_layout(
            title="Portfolio Drawdown",
            xaxis_title="Date",
            yaxis_title="Drawdown (%)",
            height=300,
            yaxis={"ticksuffix": "%"},
        )

        return fig

    except Exception as e:
        st.error(f"Error creating drawdown chart: {e}")
        return go.Figure()


def display_performance_metrics(results: BackTestResults) -> None:
    """Display performance metrics in a formatted way."""
    try:
        metrics = results["metrics"]

        # Key metrics in columns
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "Total Return",
                f"{metrics['total_return']:.2%}",
                f"{metrics['annualized_return']:.2%} annual",
            )

        with col2:
            st.metric(
                "Sharpe Ratio",
                f"{metrics['sharpe_ratio']:.2f}",
                f"Sortino: {metrics['sortino_ratio']:.2f}",
            )

        with col3:
            st.metric(
                "Max Drawdown",
                f"{metrics['max_drawdown_pct']:.2%}",
                f"{metrics['drawdown_duration']} days",
            )

        with col4:
            st.metric(
                "Win Rate",
                f"{metrics['win_rate']:.2%}",
                f"Profit Factor: {metrics['profit_factor']:.2f}",
            )

        # Detailed metrics in expandable section
        with st.expander("📊 Detailed Performance Metrics"):
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("Return Metrics")
                st.write(
                    f"**Initial Capital:** ${metrics['initial_capital']:,.2f}",
                )
                st.write(
                    f"**Final Capital:** ${metrics['final_capital']:,.2f}",
                )
                st.write(f"**Total Return:** {metrics['total_return']:.2%}")
                st.write(
                    f"**Annualized Return:** {metrics['annualized_return']:.2%}",
                )
                st.write(f"**Volatility:** {metrics['volatility']:.2%}")

                st.subheader("Risk Metrics")
                st.write(
                    f"**Max Drawdown:** {metrics['max_drawdown_pct']:.2%}",
                )
                st.write(f"**VaR (5%):** {metrics['var_5']:.2%}")
                st.write(f"**CVaR (5%):** {metrics['cvar_5']:.2%}")
                st.write(f"**Skewness:** {metrics['skewness']:.3f}")
                st.write(f"**Kurtosis:** {metrics['kurtosis']:.3f}")

            with col2:
                st.subheader("Risk-Adjusted Returns")
                st.write(f"**Sharpe Ratio:** {metrics['sharpe_ratio']:.3f}")
                st.write(f"**Sortino Ratio:** {metrics['sortino_ratio']:.3f}")
                st.write(f"**Calmar Ratio:** {metrics['calmar_ratio']:.3f}")

                st.subheader("Trade Statistics")
                st.write(f"**Total Trades:** {metrics['total_trades']}")
                st.write(f"**Win Rate:** {metrics['win_rate']:.2%}")
                st.write(f"**Profit Factor:** {metrics['profit_factor']:.2f}")
                st.write(f"**Expectancy:** ${metrics['expectancy']:.2f}")
                st.write(
                    f"**Average Trade:** ${metrics.get('avg_trade', 0):.2f}",
                )

    except Exception as e:
        st.error(f"Error displaying metrics: {e}")


def main() -> None:
    """Run the main application function."""
    # Header
    st.title("📈 Stock Market Backtesting Application")
    st.markdown("""
    A comprehensive backtesting platform for
    trading strategies using technical indicators,
    support/resistance levels, and advanced performance analysis.
    """)

    # Sidebar for configuration
    st.sidebar.title("⚙️ Configuration")

    # Data Configuration
    st.sidebar.subheader("📊 Data Settings")

    symbol = st.sidebar.text_input(
        "Stock Symbol",
        value="AAPL",
        help="Enter stock symbol (e.g., AAPL, TSLA, SPY)",
    ).upper()

    # Date range
    end_date = st.sidebar.date_input(
        "End Date",
        value=datetime.now().date(),
        max_value=datetime.now().date(),
    )

    start_date = st.sidebar.date_input(
        "Start Date",
        value=end_date - timedelta(days=365),
        max_value=end_date,
    )

    # Strategy Configuration
    st.sidebar.subheader("🎯 Strategy Settings")

    strategy_type = st.sidebar.selectbox(
        "Strategy Type",
        options=[
            "bollinger_mean_reversion",
            "trend_following",
            "cnn",
            "hybrid",
        ],
        format_func=lambda x: {
            "bollinger_mean_reversion": "Bollinger Bands Mean Reversion",
            "trend_following": "Trend Following",
            "cnn": "CNN",
            "hybrid": "Hybrid Strategy",
        }[x],
    )

    # Strategy parameters
    with st.sidebar.expander("Strategy Parameters"):
        bb_period = st.number_input(
            "Bollinger Bands Period",
            value=20,
            min_value=5,
            max_value=50,
        )
        bb_std = st.number_input(
            "Bollinger Bands Std Dev",
            value=2.0,
            min_value=1.0,
            max_value=3.0,
            step=0.1,
        )
        rsi_period = st.number_input(
            "RSI Period",
            value=14,
            min_value=5,
            max_value=30,
        )
        ma_short = st.number_input(
            "Short MA Period",
            value=10,
            min_value=5,
            max_value=50,
        )
        ma_long = st.number_input(
            "Long MA Period",
            value=20,
            min_value=10,
            max_value=100,
        )
        min_confidence = st.slider(
            "Minimum Signal Confidence",
            value=0.6,
            min_value=0.1,
            max_value=1.0,
            step=0.05,
        )

    # Backtesting Configuration
    st.sidebar.subheader("💼 Backtest Settings")

    with st.sidebar.expander("Portfolio Settings"):
        initial_capital = st.number_input(
            "Initial Capital ($)",
            value=100000,
            min_value=1000,
            step=1000,
        )
        position_size = (
            st.slider(
                "Position Size (%)",
                value=25,
                min_value=5,
                max_value=100,
                step=5,
            )
            / 100
        )
        commission = st.number_input(
            "Commission per Trade ($)",
            value=10.0,
            min_value=0.0,
            step=1.0,
        )

    with st.sidebar.expander("Risk Management"):
        enable_stop_loss = st.checkbox("Enable Stop Loss", value=True)
        stop_loss_pct = (
            st.slider(
                "Stop Loss (%)",
                value=5,
                min_value=1,
                max_value=20,
                step=1,
            )
            / 100
        )
        enable_take_profit = st.checkbox("Enable Take Profit", value=True)
        take_profit_pct = (
            st.slider(
                "Take Profit (%)",
                value=10,
                min_value=1,
                max_value=50,
                step=1,
            )
            / 100
        )

    # Load Data Button
    if st.sidebar.button("📥 Load Data", type="primary"):
        if start_date >= end_date:
            st.error("Start date must be before end date")
        else:
            data = load_data(symbol, start_date, end_date)
            if data is not None:
                st.session_state.current_data = data

    # Main content area
    if st.session_state.current_data is not None:
        data = st.session_state.current_data

        if data is None:
            raise ValueError("Data is None")
            # TODO: Should never happen. Type confusion. Refactor needed.

        # Data summary
        st.subheader(f"📊 Data Summary for {symbol}")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Records", len(data))
        with col2:
            st.metric(
                "Date Range",
                f"{data.index[0].date()} to {data.index[-1].date()}",
            )
        with col3:
            st.metric("Latest Price", f"${data['close'].iloc[-1]:.2f}")
        with col4:
            price_change = (
                data["close"].iloc[-1] - data["close"].iloc[0]
            ) / data["close"].iloc[0]
            st.metric("Period Return", f"{price_change:.2%}")

        # Strategy configuration
        strategy_config = StrategyConfig(
            strategy_type=strategy_type,
            bb_period=bb_period,
            bb_std=bb_std,
            rsi_period=rsi_period,
            ma_short_period=ma_short,
            ma_long_period=ma_long,
            min_confidence=min_confidence,
            stop_loss_pct=stop_loss_pct,
            take_profit_pct=take_profit_pct,
        )

        # Backtest configuration
        backtest_config = BacktestConfig(
            initial_capital=initial_capital,
            position_size=position_size,
            commission_per_trade=commission,
            enable_stop_loss=enable_stop_loss,
            stop_loss_pct=stop_loss_pct,
            enable_take_profit=enable_take_profit,
            take_profit_pct=take_profit_pct,
        )

        # Run Backtest Button
        if st.button("🚀 Run Backtest", type="primary"):
            with st.spinner("Running backtest..."):
                try:
                    # Create strategy
                    strategy = create_strategy(strategy_config)

                    # Run backtest
                    backtester = BacktestEngine(backtest_config)
                    results = backtester.run_backtest(data, strategy)

                    if results:
                        st.session_state.backtest_results = results
                        st.success("✅ Backtest completed successfully!")
                    else:
                        st.error("❌ Backtest failed")

                except Exception as e:
                    st.error(f"Error running backtest: {e}")
                    logger.exception(f"Backtest error: {e}")

        # Display results if available
        if st.session_state.backtest_results:
            results = st.session_state.backtest_results

            # Create tabs for different views
            tab1, tab2, tab3, tab4, tab5 = st.tabs(
                [
                    "📈 Charts",
                    "📊 Performance",
                    "💼 Trades",
                    "📉 Analysis",
                    "📋 Export",
                ],
            )

            with tab1:
                st.subheader("Price Chart with Signals")

                # Calculate indicators for display
                indicators = TechnicalIndicators()
                data_with_indicators = data.copy()

                # Add indicators
                bb_upper, bb_middle, bb_lower = indicators.bollinger_bands(
                    data["close"],
                    strategy_config.bb_period,
                    strategy_config.bb_std,
                )
                data_with_indicators["bb_upper"] = bb_upper
                data_with_indicators["bb_middle"] = bb_middle
                data_with_indicators["bb_lower"] = bb_lower

                data_with_indicators["ma_short"] = indicators.moving_average(
                    data["close"],
                    strategy_config.ma_short_period,
                )
                data_with_indicators["ma_long"] = indicators.moving_average(
                    data["close"],
                    strategy_config.ma_long_period,
                )
                data_with_indicators["rsi"] = indicators.rsi(
                    data["close"],
                    strategy_config.rsi_period,
                )

                stoch_k, stoch_d = indicators.stochastic_oscillator(
                    data["high"],
                    data["low"],
                    data["close"],
                )
                data_with_indicators["stoch_k"] = stoch_k
                data_with_indicators["stoch_d"] = stoch_d

                # Find support/resistance levels
                sr_finder = SupportResistanceFinder()
                sr_levels = sr_finder.find_support_resistance_levels(data)

                # Get signals from backtest
                strategy = create_strategy(strategy_config)
                signals = strategy.backtest(data_with_indicators)

                # Create and display chart
                fig = create_price_chart(
                    data_with_indicators,
                    signals,
                    sr_levels,
                )
                st.plotly_chart(fig, use_container_width=True)

                # Equity curve
                st.subheader("Equity Curve")
                equity_fig = create_equity_curve_chart(results)
                st.plotly_chart(equity_fig, use_container_width=True)

                # Drawdown chart
                st.subheader("Drawdown Analysis")
                drawdown_fig = create_drawdown_chart(results)
                st.plotly_chart(drawdown_fig, use_container_width=True)

            with tab2:
                st.subheader("Performance Metrics")
                display_performance_metrics(results)

            with tab3:
                st.subheader("Trade History")

                if "trades" in results and len(results["trades"]) > 0:
                    trades_df = results["trades"]

                    # Display trade summary
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        winning_trades = len(trades_df[trades_df["pnl"] > 0])
                        st.metric("Winning Trades", winning_trades)
                    with col2:
                        losing_trades = len(trades_df[trades_df["pnl"] < 0])
                        st.metric("Losing Trades", losing_trades)
                    with col3:
                        avg_trade = trades_df["pnl"].mean()
                        st.metric("Average Trade", f"${avg_trade:.2f}")

                    # Trade table
                    st.dataframe(
                        trades_df.style.format(
                            {
                                "entry_price": "${:.2f}",
                                "exit_price": "${:.2f}",
                                "pnl": "${:.2f}",
                                "pnl_pct": "{:.2%}",
                            },
                        ).background_gradient(subset=["pnl"], cmap="RdYlGn"),
                        use_container_width=True,
                    )

                    # Trade distribution chart
                    st.subheader("Trade P&L Distribution")
                    fig = px.histogram(
                        trades_df,
                        x="pnl",
                        nbins=20,
                        title="Distribution of Trade P&L",
                    )
                    fig.update_xaxes(title="P&L ($)")
                    fig.update_yaxes(title="Frequency")
                    st.plotly_chart(fig, use_container_width=True)

                else:
                    st.info("No trades were executed in this backtest")

            with tab4:
                st.subheader("Strategy Analysis")

                # Signal analysis
                if "trades" in results and len(results["trades"]) > 0:
                    trades_df = results["trades"]

                    # Monthly returns
                    if "equity_curve" in results:
                        equity_df = results["equity_curve"]

                        monthly_returns = (
                            equity_df["portfolio_value"]
                            .resample("ME")
                            .last()
                            .pct_change()
                            .dropna()
                        )

                        if len(monthly_returns) > 0:
                            st.subheader("Monthly Returns")
                            fig = px.bar(
                                x=cast(
                                    "pd.DatetimeIndex",
                                    monthly_returns.index,
                                ).strftime("%Y-%m"),
                                y=monthly_returns.to_numpy() * 100,
                                title="Monthly Returns (%)",
                            )
                            fig.update_xaxes(title="Month")
                            fig.update_yaxes(title="Return (%)")
                            st.plotly_chart(fig, use_container_width=True)

                    # Trade duration analysis
                    if "duration_days" in trades_df.columns:
                        st.subheader("Trade Duration Analysis")
                        fig = px.scatter(
                            trades_df,
                            x="duration_days",
                            y="pnl_pct",
                            color="side",
                            title="Trade Duration vs Return",
                            hover_data=["entry_price", "exit_price"],
                        )
                        fig.update_xaxes(title="Duration (Days)")
                        fig.update_yaxes(title="Return (%)")
                        st.plotly_chart(fig, use_container_width=True)

            with tab5:
                st.subheader("Export Results")

                col1, col2 = st.columns(2)

                with col1:
                    if st.button("📥 Download Trades CSV"):
                        if "trades" in results and len(results["trades"]) > 0:
                            trades_export = ExportUtils.prepare_trade_export(
                                results["trades"],
                            )
                            csv_data = ExportUtils.export_to_csv(
                                trades_export,
                                f"{symbol}_trades.csv",
                            )

                            st.download_button(
                                label="Download Trades",
                                data=csv_data.getvalue(),
                                file_name=f"{symbol}_trades_{datetime.now().strftime('%Y%m%d')}.csv",
                                mime="text/csv",
                            )
                        else:
                            st.warning("No trades to export")

                with col2:
                    if (
                        st.button("📥 Download Equity Curve CSV")
                        and "equity_curve" in results
                    ):
                        equity_export = ExportUtils.prepare_equity_export(
                            results["equity_curve"],
                        )
                        csv_data = ExportUtils.export_to_csv(
                            equity_export,
                            f"{symbol}_equity.csv",
                        )

                        st.download_button(
                            label="Download Equity Curve",
                            data=csv_data.getvalue(),
                            file_name=f"{symbol}_equity_{datetime.now().strftime('%Y%m%d')}.csv",
                            mime="text/csv",
                        )

                # Configuration summary
                with st.expander("📋 Backtest Configuration Summary"):
                    config_summary = {
                        "Symbol": symbol,
                        "Start Date": start_date.strftime("%Y-%m-%d"),
                        "End Date": end_date.strftime("%Y-%m-%d"),
                        "Strategy": strategy_type,
                        "Initial Capital": f"${initial_capital:,.2f}",
                        "Position Size": f"{position_size:.1%}",
                        "Commission": f"${commission:.2f}",
                        "Stop Loss": f"{stop_loss_pct:.1%}"
                        if enable_stop_loss
                        else "Disabled",
                        "Take Profit": f"{take_profit_pct:.1%}"
                        if enable_take_profit
                        else "Disabled",
                    }

                    for key, value in config_summary.items():
                        st.write(f"**{key}:** {value}")

    else:
        # Welcome screen
        st.info("""
        👋 **Welcome to the Stock Market Backtester!**

        To get started:
        1. Configure your data settings in the sidebar
        2. Choose your trading strategy and parameters
        3. Set up risk management rules
        4. Click "Load Data" to fetch historical data
        5. Run your backtest and analyze the results

        **Note:** You'll need a Polygon.io API key.
        Set the `POLYGON_API_KEY` environment variable.
        Get your free API key at https://polygon.io/
        """)

        # Example strategies explanation
        with st.expander("📚 Strategy Explanations"):
            st.markdown("""
            **Bollinger Bands Mean Reversion:**
            - Buys when price touches lower Bollinger Band (oversold)
            - Sells when price touches upper Bollinger Band (overbought)
            - Uses RSI and Stochastic for confirmation
            - Best for sideways/ranging markets

            **Trend Following:**
            - Uses moving average crossovers to identify trend changes
            - Buys on bullish MA crossover with MACD confirmation
            - Sells on bearish MA crossover
            - Best for trending markets

            **Hybrid Strategy:**
            - Automatically switches between mean reversion and trend following
            - Uses market regime detection to choose appropriate strategy
            - Adapts to changing market conditions
            """)


if __name__ == "__main__":
    main()
