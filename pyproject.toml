[project]
name = "broker-backtester"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "matplotlib>=3.10.3",
    "mypy>=1.16.1",
    "numpy>=2.3.0",
    "pandas>=2.3.0",
    "pandas-stubs>=2.2.3.250527",
    "plotly>=6.1.2",
    "plotly-stubs>=0.0.5",
    "polygon-api-client>=1.14.6",
    "python-dotenv>=1.1.0",
    "requests>=2.32.4",
    "scipy>=1.15.3",
    "scipy-stubs>=********",
    "streamlit>=1.46.0",
    "torch>=2.7.1",
    "zuban>=0.0.17",
]
[tool.ruff]
line-length = 79
lint.extend-select = ["ALL"]
lint.ignore = [
    "ANN204", # Missing return type annotation for special method `__init__`
    "COM812", # Trailing comma missing
    "D107",   # Missing docstring in `__init__`,
    "EM102",  # Exception must not use an f-string literal, assign to variable first
    "G004",   # Logging statement uses f-string
    "TD002",  # Missing author in TODO
    "TD003",  # Missing issue link for this TODO
    "TRY003", # Avoid specifying long messages outside the exception class
    "TRY300", # Consider moving this statement to an `else` block,
    "TRY401", # Redundant exception object included in `logging.exception`
    "EM101",  # Exception must not use a string literal, assign to variable first
    "DTZ005", # `datetime.datetime.now()` called without a `tz` argument
    "ERA001", # Found commented-out code
    "FBT002", # Boolean default positional argument in function definition
    "FBT001", # Boolean-typed positional argument in function definition
]
lint.select = [
    "COM819", #	prohibited-trailing-comma
]
