{"[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.wordWrapColumn": 79, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.ruff": "explicit", "source.fixAll": "explicit", "source.organizeImports": "explicit", "source.removeUnusedImports": "explicit"}}, "python.defaultInterpreterPath": "./.venv/Scripts/python.exe", "python.languageServer": "Pylsp", "pylsp.plugins.ruff.enabled": true, "pylsp.plugins.ruff.lineLength": 79, "pylsp.plugins.mypy.enabled": true, "pylsp.plugins.mypy.live_mode": true}