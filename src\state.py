"""."""

import logging
from datetime import date, timedelta

import pandas as pd
import streamlit as st
from dotenv import load_dotenv

from src.backtest.results import BackTestResults
from src.data_provider import PolygonDataProvider
from src.temp import BacktestConfig, StrategyConfig
from src.utils import setup_logging

load_dotenv()

setup_logging()
logger = logging.getLogger(__name__)


class AppState:
    """Application state manager.

    Store and manage application state.
    """

    _symbol: str
    strategy_config: StrategyConfig
    backtest_config: BacktestConfig
    _start_date: date | None = None
    _end_date: date | None = None

    def __init__(self) -> None:
        self.data_provider = None
        self.current_data = None
        self.backtest_results = None

    @property
    def data_provider(self) -> PolygonDataProvider | None:
        """Return the data provider."""
        data: PolygonDataProvider = st.session_state.data_provider
        return data

    @data_provider.setter
    def data_provider(self, reference: PolygonDataProvider | None) -> None:
        st.session_state.data_provider = reference

    @data_provider.deleter
    def data_provider(self) -> None:
        st.session_state.data_provider = None

    @property
    def current_data(self) -> pd.DataFrame | None:
        """Return the currently loaded data."""
        data: pd.DataFrame = st.session_state.current_data
        return data

    @current_data.setter
    def current_data(self, reference: pd.DataFrame | None) -> None:
        st.session_state.current_data = reference

    @current_data.deleter
    def current_data(self) -> None:
        st.session_state.current_data = None

    @property
    def backtest_results(self) -> BackTestResults | None:
        """Return the cached backtest results."""
        data: BackTestResults = st.session_state.backtest_results
        return data

    @backtest_results.setter
    def backtest_results(self, reference: BackTestResults | None) -> None:
        st.session_state.backtest_results = reference

    @backtest_results.deleter
    def backtest_results(self) -> None:
        st.session_state.backtest_results = None

    @property
    def symbol(self) -> str:
        """Return the currently selected symbol."""
        return self._symbol

    @symbol.setter
    def symbol(self, value: str) -> None:
        self._symbol = value

    @symbol.deleter
    def symbol(self) -> None:
        del self._symbol

    @property
    def start_date(self) -> date:
        """Return the start date."""
        if self._start_date is None:
            return date.today() - timedelta(days=365)
        return self._start_date

    @start_date.setter
    def start_date(self, value: date) -> None:
        self._start_date = value

    @start_date.deleter
    def start_date(self) -> None:
        del self._start_date

    @property
    def end_date(self) -> date:
        """Return the end date."""
        if self._end_date is None:
            return date.today()
        return self._end_date

    @end_date.setter
    def end_date(self, value: date) -> None:
        self._end_date = value

    @end_date.deleter
    def end_date(self) -> None:
        del self._end_date
